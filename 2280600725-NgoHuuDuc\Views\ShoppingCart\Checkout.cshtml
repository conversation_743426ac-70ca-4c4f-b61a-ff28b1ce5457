@model Order
@using Microsoft.EntityFrameworkCore
@using NgoHuuDuc_2280600725.Data
@using NgoHuuDuc_2280600725.Models
@inject ApplicationDbContext Context

<div class="container mt-4">
    <h2 class="mb-4">Checkout</h2>

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Shipping Information</h5>
                </div>
                <div class="card-body">
                    <form asp-action="Checkout" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="mb-3">
                            <label for="ShippingAddress" class="form-label">Shipping Address</label>
                            <textarea id="ShippingAddress" name="ShippingAddress" class="form-control" rows="3" required></textarea>
                            <span asp-validation-for="ShippingAddress" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label for="Notes" class="form-label">Order Notes (Optional)</label>
                            <textarea id="Notes" name="Notes" class="form-control" rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Phương thức thanh toán</label>
                            <div class="payment-methods">
                                <div class="form-check payment-option">
                                    <input class="form-check-input" type="radio" name="PaymentMethod" id="cod" value="0" checked>
                                    <label class="form-check-label payment-label" for="cod">
                                        <div class="payment-icon">
                                            <i class="fas fa-truck"></i>
                                        </div>
                                        <div class="payment-info">
                                            <strong>Thanh toán khi nhận hàng (COD)</strong>
                                            <small class="text-muted d-block">Thanh toán bằng tiền mặt khi nhận hàng</small>
                                        </div>
                                    </label>
                                </div>
                                <div class="form-check payment-option">
                                    <input class="form-check-input" type="radio" name="PaymentMethod" id="online" value="1">
                                    <label class="form-check-label payment-label" for="online">
                                        <div class="payment-icon">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <div class="payment-info">
                                            <strong>Thanh toán online</strong>
                                            <small class="text-muted d-block">Thanh toán qua thẻ tín dụng, ví điện tử</small>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Place Order</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Order Summary</h5>
                </div>
                <div class="card-body">
                    @{
                        var userId = User.Identity?.Name;
                        var cart = await Context.Carts
                            .Include(c => c.Items)
                            .FirstOrDefaultAsync(c => c.UserId == userId);

                        if (cart != null && cart.Items.Any())
                        {
                            <ul class="list-group mb-3">
                                @foreach (var item in cart.Items)
                                {
                                    <li class="list-group-item d-flex justify-content-between lh-sm">
                                        <div>
                                            <h6 class="my-0">@item.ProductName</h6>
                                            <small class="text-muted">@item.Quantity x @item.Price.ToString("C")</small>
                                        </div>
                                        <span class="text-muted">@((item.Price * item.Quantity).ToString("C"))</span>
                                    </li>
                                }
                                <li class="list-group-item d-flex justify-content-between">
                                    <span>Total</span>
                                    <strong>@cart.Items.Sum(i => i.Price * i.Quantity).ToString("C")</strong>
                                </li>
                            </ul>
                        }
                        else
                        {
                            <div class="alert alert-warning">
                                Your cart is empty. Please add items to your cart before checkout.
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}

<style>
.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.payment-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
    margin-bottom: 0;
}

.payment-option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
}

.payment-option input[type="radio"]:checked + .payment-label {
    color: #007bff;
}

.payment-option input[type="radio"]:checked {
    border-color: #007bff;
}

.payment-option input[type="radio"]:checked ~ * {
    border-color: #007bff;
}

.payment-option:has(input[type="radio"]:checked) {
    border-color: #007bff;
    background-color: #e7f3ff;
}

.payment-label {
    display: flex;
    align-items: center;
    gap: 15px;
    cursor: pointer;
    margin-bottom: 0;
    width: 100%;
}

.payment-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #f8f9fa;
    border-radius: 50%;
    color: #6c757d;
    font-size: 18px;
}

.payment-option:has(input[type="radio"]:checked) .payment-icon {
    background-color: #007bff;
    color: white;
}

.payment-info {
    flex: 1;
}

.payment-info strong {
    display: block;
    margin-bottom: 4px;
}

.form-check-input {
    margin-top: 0;
    margin-right: 10px;
}
</style>
